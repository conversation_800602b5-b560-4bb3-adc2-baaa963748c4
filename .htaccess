RewriteEngine On

# Redirect to HTTPS and non-www, but NOT on localhost
RewriteCond %{HTTP_HOST} !^localhost$ [NC]
RewriteCond %{HTTP_HOST} ^www\.serimpro\.com\.co$ [NC]
RewriteRule ^(.*)$ https://serimpro.com.co/$1 [L,R=301]

RewriteCond %{HTTP_HOST} ^serimpro\.com\.co$ [NC]
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://serimpro.com.co/$1 [L,R=301]


RewriteRule ^index/?$ index.php

RewriteRule ^quienes_somos/?$ src/web/quienes_somos.php
RewriteRule ^estrategias-corporativas/?$ src/web/estrategias.php
RewriteRule ^mision/?$ src/web/mision.php
RewriteRule ^vision/?$ src/web/vision.php
RewriteRule ^valores/?$ src/web/valores.php
RewriteRule ^contactanos/?$ src/web/contactanos.php
RewriteRule ^proyectos/?$ src/web/proyectos.php
RewriteRule ^politica-tratamiento-datos/?$ src/web/politica_tratamiento_datos.php
RewriteRule ^politica-responsabilidad-cumplimiento/?$ src/web/politica_responsabilidad.php
RewriteRule ^politica-confidencialidad/?$ src/web/politica_confidencialidad.php
RewriteRule ^politica-uso-plataformas/?$ src/web/politica_uso_plataformas.php
RewriteRule ^politica-privacidad/?$ src/web/politica_privacidad.php
RewriteRule ^politica-pago-comisiones/?$ src/web/politica_pago_comisiones.php

RewriteRule ^admin/login/?$ src/admin/login.php
RewriteRule ^admin/dashboard/?$ src/admin/dashboard.php
RewriteRule ^admin/lusuarios/?$ src/admin/lusuarios.php
RewriteRule ^admin/iusuario/?$ src/admin/iusuario.php
RewriteRule ^admin/listado-servicios-categorias/?$ src/admin/lservicios_categorias.php
RewriteRule ^admin/listado-servicios/?$ src/admin/lservicios.php
RewriteRule ^admin/listado-partners/?$ src/admin/lpartners.php
RewriteRule ^admin/listado-usuarios/?$ src/admin/lusuarios.php
RewriteRule ^admin/cambiar_clave/?$ src/admin/cambiar_clave.php
RewriteRule ^admin/nuestra-empresa-general/?$ src/admin/aempresa_general.php
RewriteRule ^admin/inicio-banner/?$ src/admin/ainicio_banner.php

RewriteRule ^admin/cerrar/?$ src/sessions/cerrar.php
